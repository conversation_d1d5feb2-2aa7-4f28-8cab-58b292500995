#!/usr/bin/env python3
"""
4-Level Aggregation/Disaggregation Sankey Diagram for Strasbourg Mobility Flows
Demonstrates hierarchical flow visualization with aggregation→disaggregation pattern.
"""

import os
import pandas as pd
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher


def process_csv_to_4_level_flows(df):
    """
    Transform CSV data into 4-level aggregation/disaggregation flows.

    Corrected 4-Level Structure:
    1. Origins: ONLY Strasbourg communes (for intra-Strasbourg flows)
    2. Origin Aggregation: "Strasbourg" (aggregated from Level 1) + External zones
    3. Destination Aggregation: "Strasbourg" (to disaggregate to Level 4) + External
    4. Destinations: ONLY Strasbourg communes (disaggregated from Level 3)

    This creates proper aggregation→disaggregation pattern where:
    - Level 1→2: Strasbourg communes aggregate to "Strasbourg" department
    - Level 2→3: Department-to-department flows (including external zones)
    - Level 3→4: "Strasbourg" department disaggregates to communes
    """
    flow_data = []

    for _, row in df.iterrows():
        origin = row["origin_zone"]
        destination = row["destination_zone"]
        count = int(row["count"])

        # Skip if count is 0
        if count == 0:
            continue

        # Check if origin/destination are Strasbourg communes
        is_origin_strasbourg = origin.startswith("Strasbourg")
        is_dest_strasbourg = destination.startswith("Strasbourg")

        # Create 4-level flow tuples based on origin/destination types
        if is_origin_strasbourg and is_dest_strasbourg:
            # Strasbourg to Strasbourg: commune → "Strasbourg" → "Strasbourg" → commune
            # This shows intra-Strasbourg flows with aggregation/disaggregation
            flow_tuple = (origin, "Strasbourg", "Strasbourg", destination, count)
            flow_data.append(flow_tuple)
        elif is_origin_strasbourg and not is_dest_strasbourg:
            # Strasbourg to external: Skip to maintain Level 4 = Strasbourg only
            # We need Level 4 to contain only Strasbourg communes for proper
            # disaggregation pattern
            continue
        elif not is_origin_strasbourg and is_dest_strasbourg:
            # External to Strasbourg: Skip to maintain Level 1 = Strasbourg only
            # We need Level 1 to contain only Strasbourg communes for proper
            # aggregation pattern
            continue
        else:
            # External to external: Skip as it doesn't involve Strasbourg communes
            continue

    return flow_data


def create_strasbourg_aggregation_flow():
    """Create 4-level aggregation/disaggregation flow chart for Strasbourg mobility."""

    # CSV file path - use the file in the workspace
    csv_path = "hv_aggregated_od_matrix_total_RN4_Est_Ouest.csv"

    # Output directory
    output_dir = "output_flow_charts"
    os.makedirs(output_dir, exist_ok=True)

    # Read CSV data
    print(f"Reading CSV data from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows of flow data")

    # Process CSV data into 4-level structure
    flow_data = process_csv_to_4_level_flows(df)
    print(f"Generated {len(flow_data)} 4-level flow records")

    # Extract unique categories from the processed flow data
    all_categories = set()
    for level1, level2, level3, level4, count in flow_data:
        all_categories.update([level1, level2, level3, level4])

    # Expand to individual flow tuples
    flows_4_level = []
    for level1, level2, level3, level4, count in flow_data:
        flows_4_level.extend([(level1, level2, level3, level4)] * count)

    # Create categories order from the data
    strasbourg_communes = sorted(
        [
            cat
            for cat in all_categories
            if cat.startswith("Strasbourg") and cat != "Strasbourg"
        ]
    )
    external_zones = sorted(
        [cat for cat in all_categories if not cat.startswith("Strasbourg")]
    )

    categories_order = strasbourg_communes + ["Strasbourg"] + external_zones
    print(f"Categories found: {len(categories_order)} total")
    print(f"Strasbourg communes: {len(strasbourg_communes)}")
    print(f"External zones: {len(external_zones)}")

    # Generate dynamic colors and labels
    colors = {}
    categories_labels = {}

    # Color schemes
    strasbourg_colors = [
        "#E53935",
        "#1E88E5",
        "#43A047",
        "#9C27B0",
        "#FF5722",
        "#795548",
        "#607D8B",
    ]

    # Assign colors and labels for Strasbourg communes
    for i, commune in enumerate(strasbourg_communes):
        colors[commune] = strasbourg_colors[i % len(strasbourg_colors)]
        # Simplify long Strasbourg commune names
        if " - " in commune:
            categories_labels[commune] = commune.split(" - ", 1)[1]
        else:
            categories_labels[commune] = commune.replace("Strasbourg ", "")

    # Strasbourg department
    colors["Strasbourg"] = "#FF9800"
    categories_labels["Strasbourg"] = "Strasbourg"

    # Level titles explaining the corrected aggregation/disaggregation pattern
    level_titles = [
        "Strasbourg Communes (Origins)",  # Level 1: Only Strasbourg communes
        "Aggregated Origins",  # Level 2: "Strasbourg" department
        "Aggregated Destinations",  # Level 3: "Strasbourg" department
        "Strasbourg Communes (Destinations)",  # Level 4: Only Strasbourg communes
    ]

    grapher = FlowChartGrapher[str](output_dir)

    # Generate the aggregation/disaggregation flow chart with custom font sizes
    output_file = grapher.make_multilevel_chart(
        file_name="strasbourg_aggregation_disaggregation",
        title="Strasbourg Mobility: Aggregation/Disaggregation Flow Analysis",
        flows=flows_4_level,
        level_titles=level_titles,
        colors=colors,
        categories_order=categories_order,
        categories_labels=categories_labels,
        categories_icons={},  # No icons to avoid default "car" icons
        level_spacing=350,  # Wider spacing for complex labels
        main_title_font_size_px=32,  # Smaller main title
        title_font_size_px=28,  # Smaller level titles
        line_header_font_size_px=24,  # Smaller category labels
        number_font_size_px=18,  # Smaller numbers
    )

    print(f"Aggregation/Disaggregation flow chart generated: {output_file}")
    print("\nCorrected Flow Pattern Explanation:")
    print("Level 1: ONLY Strasbourg communes (intra-commune origins)")
    print("Level 2: Strasbourg communes aggregate to 'Strasbourg' department")
    print("Level 3: 'Strasbourg' department (ready for disaggregation)")
    print("Level 4: ONLY Strasbourg communes (intra-commune destinations)")
    print("This shows pure intra-Strasbourg mobility with aggregation/disaggregation")

    return output_file


if __name__ == "__main__":
    create_strasbourg_aggregation_flow()
