#!/usr/bin/env python3
"""
Test script to verify the corrected 4-level flow processing logic.
"""

import pandas as pd
from strasbourg_aggregation_flow import process_csv_to_4_level_flows

def test_corrected_flow_processing():
    """Test the corrected CSV processing function."""
    
    # Create sample data that includes various flow types
    sample_data = {
        'origin_zone': [
            'Strasbourg - Centre',
            'Strasbourg - Gare', 
            'Strasbourg quartiers est',
            'EMS_1ere couronne sud',  # External to Strasbourg
            'Strasbourg - Krutenau',
            'BMNord_Sud Ouest',  # External to external
            'HorsZone',  # External to Strasbourg
        ],
        'destination_zone': [
            'Strasbourg - Gare',
            'Strasbourg quartiers est',
            'Strasbourg - Centre', 
            'Strasbourg - Centre',  # External to Strasbourg
            'EMS_Couronne Nord',  # Strasbourg to external
            'EMS_1ere couronne sud',  # External to external
            'Strasbourg quartiers sud',  # External to Strasbourg
        ],
        'count': [25, 20, 15, 30, 22, 18, 14]
    }
    
    df = pd.DataFrame(sample_data)
    print("Sample CSV data:")
    print(df)
    print()
    
    # Process the data
    flow_data = process_csv_to_4_level_flows(df)
    
    print(f"Generated {len(flow_data)} 4-level flow records:")
    for i, (level1, level2, level3, level4, count) in enumerate(flow_data):
        print(f"{i+1:2d}. {level1:25} → {level2:15} → {level3:15} → {level4:25} ({count:2d})")
    
    print()
    
    # Verify the corrected logic
    print("Verification of Corrected Logic:")
    print("✓ All Level 1 origins should be Strasbourg communes")
    print("✓ All Level 4 destinations should be Strasbourg communes")
    print("✓ Only intra-Strasbourg flows should be included")
    
    # Check that all flows are intra-Strasbourg
    all_strasbourg_origins = all(f[0].startswith('Strasbourg') for f in flow_data)
    all_strasbourg_destinations = all(f[3].startswith('Strasbourg') for f in flow_data)
    all_level2_strasbourg = all(f[1] == 'Strasbourg' for f in flow_data)
    all_level3_strasbourg = all(f[2] == 'Strasbourg' for f in flow_data)
    
    print(f"All Level 1 are Strasbourg communes: {all_strasbourg_origins}")
    print(f"All Level 2 are 'Strasbourg': {all_level2_strasbourg}")
    print(f"All Level 3 are 'Strasbourg': {all_level3_strasbourg}")
    print(f"All Level 4 are Strasbourg communes: {all_strasbourg_destinations}")
    
    if all([all_strasbourg_origins, all_strasbourg_destinations, all_level2_strasbourg, all_level3_strasbourg]):
        print("\n✅ SUCCESS: Corrected 4-level structure is working properly!")
        print("   - Level 1: Only Strasbourg communes (origins)")
        print("   - Level 2: Aggregated to 'Strasbourg' department")
        print("   - Level 3: 'Strasbourg' department (ready for disaggregation)")
        print("   - Level 4: Only Strasbourg communes (destinations)")
    else:
        print("\n❌ ERROR: Structure validation failed!")

if __name__ == "__main__":
    test_corrected_flow_processing()
